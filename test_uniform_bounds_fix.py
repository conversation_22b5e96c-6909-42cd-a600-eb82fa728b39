#!/usr/bin/env python3
"""
Test script to reproduce and fix the "check_uniform_bounds" not implemented for 'Byte' error.
"""

import torch
import sys
import os

def test_uniform_bounds_error():
    """Reproduce the uniform_bounds error with different data types."""
    
    print("🔍 Testing uniform_bounds error with different data types...")
    
    # Test different data types
    test_cases = [
        (torch.float32, "float32", True),
        (torch.float16, "float16", True),
        (torch.bfloat16, "bfloat16", True),
        (torch.uint8, "uint8 (Byte)", False),  # This will fail
        (torch.int8, "int8", False),           # This might fail
        (torch.int16, "int16", False),         # This might fail
        (torch.int32, "int32", False),         # This might fail
        (torch.int64, "int64", False),         # This might fail
    ]
    
    shape = [2, 32, 100, 1024]
    
    for dtype, name, should_work in test_cases:
        print(f"\nTesting {name}...")
        
        try:
            tensor = torch.zeros(shape, dtype=dtype)
            tensor.uniform_(0, 100)
            
            if should_work:
                print(f"✅ {name}: uniform_() works as expected")
            else:
                print(f"⚠️ {name}: uniform_() works unexpectedly (might be PyTorch version dependent)")
                
        except RuntimeError as e:
            if "check_uniform_bounds" in str(e):
                print(f"❌ {name}: uniform_() failed with check_uniform_bounds error")
            else:
                print(f"❌ {name}: uniform_() failed with other error: {e}")
        except Exception as e:
            print(f"❌ {name}: uniform_() failed with unexpected error: {e}")

def create_safe_test_data(tensor, seed=42, value_range=(0, 100)):
    """Create test data safely for any tensor type."""
    
    torch.manual_seed(seed)
    
    # Method 1: Try uniform_ for floating point types
    if tensor.dtype.is_floating_point:
        try:
            tensor.uniform_(value_range[0], value_range[1])
            return True
        except Exception:
            pass
    
    # Method 2: Use normal_ for floating point types
    if tensor.dtype.is_floating_point:
        try:
            tensor.normal_(mean=50, std=20)  # Roughly 0-100 range
            tensor.clamp_(value_range[0], value_range[1])
            return True
        except Exception:
            pass
    
    # Method 3: Use randint for integer types
    if not tensor.dtype.is_floating_point:
        try:
            # Create random integer tensor and copy
            random_tensor = torch.randint(
                value_range[0], 
                value_range[1] + 1, 
                tensor.shape, 
                dtype=torch.int64
            )
            tensor.copy_(random_tensor.to(tensor.dtype))
            return True
        except Exception:
            pass
    
    # Method 4: Fill with constant value
    try:
        tensor.fill_(42)
        return True
    except Exception:
        pass
    
    # Method 5: Use random_ for any type
    try:
        tensor.random_(value_range[0], value_range[1] + 1)
        return True
    except Exception:
        pass
    
    return False

def test_safe_data_creation():
    """Test the safe data creation function with different types."""
    
    print("\n🧪 Testing safe data creation function...")
    
    test_cases = [
        torch.float32,
        torch.float16,
        torch.bfloat16,
        torch.uint8,
        torch.int8,
        torch.int16,
        torch.int32,
        torch.int64,
    ]
    
    shape = [2, 32, 100, 1024]
    
    for dtype in test_cases:
        print(f"Testing safe data creation for {dtype}...")
        
        tensor = torch.zeros(shape, dtype=dtype)
        success = create_safe_test_data(tensor, seed=42)
        
        if success:
            print(f"✅ Safe data creation successful for {dtype}")
            print(f"   Data range: [{tensor.min().item():.2f}, {tensor.max().item():.2f}]")
        else:
            print(f"❌ Safe data creation failed for {dtype}")

def test_memory_allocator_types():
    """Test what data types the memory allocator might return."""
    
    print("\n🔬 Testing potential memory allocator data types...")
    
    # Simulate different scenarios that might occur
    test_scenarios = [
        ("Standard bfloat16", torch.bfloat16),
        ("Standard float32", torch.float32),
        ("Potential uint8", torch.uint8),  # This might cause the error
        ("Potential int8", torch.int8),
    ]
    
    shape = [2, 32, 100, 1024]
    
    for scenario_name, dtype in test_scenarios:
        print(f"\nScenario: {scenario_name}")
        
        # Simulate memory object
        class MockMemoryObj:
            def __init__(self, shape, dtype):
                self.raw_data = torch.zeros(shape, dtype=dtype)
                self._ref_count = 0
            
            def ref_count_up(self):
                self._ref_count += 1
            
            def get_ref_count(self):
                return self._ref_count
        
        memory_obj = MockMemoryObj(shape, dtype)
        memory_obj.ref_count_up()
        
        # Test current approach (might fail)
        print("  Testing current approach (zeros_like + uniform_)...")
        try:
            torch.manual_seed(42)
            test_tensor = torch.zeros_like(memory_obj.raw_data)
            test_tensor.uniform_(0, 100)
            memory_obj.raw_data.copy_(test_tensor)
            print("  ✅ Current approach works")
        except Exception as e:
            print(f"  ❌ Current approach failed: {e}")
        
        # Test safe approach
        print("  Testing safe approach...")
        try:
            success = create_safe_test_data(memory_obj.raw_data, seed=42)
            if success:
                print("  ✅ Safe approach works")
            else:
                print("  ❌ Safe approach failed")
        except Exception as e:
            print(f"  ❌ Safe approach failed with exception: {e}")

def create_fixed_test_data_function():
    """Create the fixed function for test files."""
    
    code = '''
def fill_test_data_safely(memory_obj, seed=42, value_range=(0, 100)):
    """Fill memory object with test data safely, handling all data types."""
    
    torch.manual_seed(seed)
    tensor = memory_obj.raw_data
    
    # Method 1: Try uniform_ for floating point types
    if tensor.dtype.is_floating_point:
        try:
            test_tensor = torch.zeros_like(tensor)
            test_tensor.uniform_(value_range[0], value_range[1])
            tensor.copy_(test_tensor)
            return
        except Exception:
            pass
    
    # Method 2: Use normal_ for floating point types
    if tensor.dtype.is_floating_point:
        try:
            test_tensor = torch.zeros_like(tensor)
            test_tensor.normal_(mean=50, std=20)
            test_tensor.clamp_(value_range[0], value_range[1])
            tensor.copy_(test_tensor)
            return
        except Exception:
            pass
    
    # Method 3: Use randint for integer types
    try:
        random_tensor = torch.randint(
            value_range[0], 
            value_range[1] + 1, 
            tensor.shape, 
            dtype=torch.int64
        )
        tensor.copy_(random_tensor.to(tensor.dtype))
        return
    except Exception:
        pass
    
    # Method 4: Fill with constant (fallback)
    try:
        tensor.fill_(42)
        return
    except Exception:
        pass
    
    raise RuntimeError(f"Could not fill tensor with dtype {tensor.dtype}")
'''
    
    print("\n📝 Fixed function for test files:")
    print(code)
    return code

if __name__ == "__main__":
    try:
        print("🧪 Uniform Bounds Error Fix Analysis\n")
        
        test_uniform_bounds_error()
        test_safe_data_creation()
        test_memory_allocator_types()
        create_fixed_test_data_function()
        
        print("\n📋 Summary:")
        print("- uniform_() only works with floating point types")
        print("- Integer types (including uint8/Byte) don't support uniform_()")
        print("- Need to use different methods for different data types")
        print("- Safe approach: check dtype and use appropriate method")
        
        print("\n🎯 Recommendation:")
        print("Replace the current uniform_() approach with a safe function")
        print("that handles all data types appropriately.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

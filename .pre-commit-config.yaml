repos:
- repo: https://github.com/PyCQA/isort
  rev: 6.0.1
  hooks:
  - id: isort
- repo: https://github.com/astral-sh/ruff-pre-commit
  rev: v0.11.7
  hooks:
  - id: ruff
    args: [--output-format, github, --fix]
  - id: ruff-format
- repo: https://github.com/codespell-project/codespell
  rev: v2.4.1
  hooks:
  - id: codespell
    additional_dependencies: ['tomli']
    args: ['--toml', 'pyproject.toml']
- repo: https://github.com/pre-commit/mirrors-clang-format
  rev: v20.1.3
  hooks:
  - id: clang-format
    types_or: [c++, cuda]
    args: [--style=file, --verbose]
-   repo: https://github.com/pre-commit/mirrors-mypy
    rev: 'v1.15.0'
    hooks:
    - id: mypy
      #args: [--strict, --ignore-missing-imports]
      additional_dependencies: [tokenize-rt==6.1.0]  # For better dynamic analysis performance

/*
 * Copyright 2024-2025 LMCache Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Adapted from
 * https://github.com/vllm-project/vllm/blob/main/csrc/cuda_compat.h
 */

#pragma once
#ifndef USE_ROCM
  #define LMCACHE_LDG(arg) __ldg(arg)
#else
  #define LMCACHE_LDG(arg) *(arg)
#endif
# Common project dependencies
-r common.txt

# Dependencies for NVIDIA GPUs
ray >= 2.9
nvidia-ml-py # for pynvml package

# These must be updated alongside torch to correspond to vLLM versions
torch == 2.7.0
torchvision == 0.22.0   # Required for phi3v processor. See https://github.com/pytorch/vision?tab=readme-ov-file#installation for corresponding version
xformers == 0.0.30; platform_system == 'Linux' and platform_machine == 'x86_64'  # Requires PyTorch 2.7.0

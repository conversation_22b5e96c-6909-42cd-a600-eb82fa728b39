# Copyright 2024-2025 LMCache Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Standard
from typing import Optional, Sequence, Tuple
import abc

# First Party
from lmcache.utils import CacheEngineKey


class LookupServerInterface(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def lookup(self, key: <PERSON>ache<PERSON>ngine<PERSON>ey) -> Optional[Tuple[str, int]]:
        """
        Perform lookup in the lookup server.
        """
        raise NotImplementedError

    @abc.abstractmethod
    def insert(
        self,
        key: <PERSON>acheEngine<PERSON><PERSON>,
    ):
        """
        Perform insert in the lookup server.
        """
        raise NotImplementedError

    @abc.abstractmethod
    def batched_insert(
        self,
        key: Sequence[CacheEngineKey],
    ):
        """
        Perform batched insert in the lookup server.
        """
        raise NotImplementedError

    @abc.abstractmethod
    def remove(
        self,
        key: CacheEngineKey,
    ):
        """
        Perform remove in the lookup server.
        """
        raise NotImplementedError

    @abc.abstractmethod
    def batched_remove(
        self,
        keys: Sequence[CacheEngineKey],
    ):
        """
        Perform batched remove in the lookup server.
        """
        raise NotImplementedError

steps:
  - label: ":pip: Install dependencies"
    key: "pip"
    command: pip install -r requirements/common.txt
    plugins:
      - docker#v5.9.0:
          image: "python:3.10-slim"

  - label: ":pip: Install test dependencies"
    key: "pip-test"
    depends_on: ["pip"]
    command: pip install -r requirements/test.txt
    plugins:
      - docker#v5.9.0:
          image: "python:3.10-slim"

# - label: Lint with Ruff
#   depends_on: ["pip"]
#   command: |
#     pip install ruff
#     ruff check .
#   plugins:
#     - docker#v5.9.0:
#         image: "python:3.13"

  - label: ":pytest: Run pytest"
    key: "pytest"
    depends_on: ["pip", "pip-test"]
    command: |
      pip install pytest
      pytest --junitxml=junit/test-results.xml \
    artifact_paths:
      - junit/test-results.xml
    plugins:
      - docker#v5.9.0:
          image: "python:3.13"

  - label: ":junit: <PERSON>otate"
    depends_on: ["pytest"]
    plugins:
      - junit-annotate#v2.4.1:
          artifacts: junit/*.xml

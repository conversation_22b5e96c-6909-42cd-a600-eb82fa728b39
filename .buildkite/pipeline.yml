env:
  PATH: "$HOME/.local/bin:$PATH"

steps:
  - label: ":pip: Prepare venv"
    key: "venv"
    plugins:
      - cache#v1.5.2:
          key: "venv-{{ checksum \"requirements/common.txt\" }}-{{ checksum \"requirements/test.txt\" }}"
          path: ".venv"
          save: "pipeline"
    command:
      - bash .buildkite/install-env.sh

  - label: ":pytest: Run pytest"
    key: "pytest"
    depends_on:
      - "venv"
    timeout_in_minutes: 25
    plugins:
      - cache#v1.5.2:
          key: "venv-{{ checksum \"requirements/common.txt\" }}-{{ checksum \"requirements/test.txt\" }}"
          path: ".venv"
          restore: "pipeline"
    command:
      - bash .buildkite/install-lmcache.sh
      - source .venv/bin/activate
      - |
        LMCACHE_TRACK_USAGE="false" \
        coverage run --source=lmcache/ -m pytest -xsv \
          --junitxml=junit/test-results.xml \
          --ignore=tests/disagg \
          --ignore=tests/v1/test_pos_kernels.py
      - coverage report -m > coverage.txt
    artifact_paths:
      - "junit/test-results.xml"
      - "coverage.txt"

  - label: ":junit: Annotate"
    depends_on:
      - "pytest"
    plugins:
      - junit-annotate#v2.4.1:
          artifacts: "junit/*.xml"

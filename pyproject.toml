[build-system]
# Should be mirrored in requirements/build.txt
requires = [
    "ninja",
    "packaging>=24.2",
    "setuptools>=77.0.3,<81.0.0",
    "setuptools_scm>=8",
    "torch==2.7.0",
    "wheel",
]
build-backend = "setuptools.build_meta"

[project]
name = "lmcache"
authors = [{name = "LMCache Team", email = "<EMAIL>"}]
license = "Apache-2.0"
license-files = ["LICENSE"]
readme = "README.md"
description = "A LLM serving engine extension to reduce TTFT and increase throughput, especially under long-context scenarios."
classifiers = [
    "Development Status :: 3 - Alpha",
    "Operating System :: POSIX :: Linux",
    "Environment :: GPU",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: Implementation :: CPython",
]
requires-python = ">=3.10,<3.13"
dynamic = ["dependencies", "version"]

[project.scripts]
lmcache_v0_server="lmcache.server.__main__:main"
lmcache_server="lmcache.v1.server.__main__:main"
lmcache_controller="lmcache.v1.api_server.__main__:main"

[project.urls]
homepage = "https://docs.lmcache.ai"
source = "https://github.com/LMCache/LMCache"
issues = "https://github.com/LMCache/LMCache"

[tool.setuptools_scm]
version_file = "lmcache/_version.py"
# do not include +gREV local version, required for Test PyPI upload
local_scheme = "no-local-version"

[tool.setuptools.dynamic]
dependencies = { file = ["requirements/common.txt"] }

[tool.setuptools.packages.find]
where = [""]
include = ["lmcache", "lmcache*"]

[tool.ruff]
# same as Black's default line length
line-length = 88

[tool.ruff.lint]
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    # "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    #"SIM",
    # Ruff does not support isort's import_headings feature, yet.
    # "I",
    # flake8-logging-format
    #"G",
]
ignore = [
    # star imports
    "F405", "F403",
    # lambda expression assignment
    "E731",
    # Loop control variable not used within loop body
    "B007",
    # f-string format
    "UP032",
]

[tool.ruff.lint.isort]
# same as .isort.cfg
from-first = true
# not supported yet
# import-heading-future=Future
# import-heading-stdlib=Standard
# import-heading-thirdparty=Third Party
# import-heading-firstparty=First Party
# import-heading-localfolder=Local

[tool.mypy]
modules = ["lmcache", "tests"]

# TODO: tighten MyPy checks by enabling these checks over time.
disable_error_code = [
    "annotation-unchecked",
    "union-attr",
    "var-annotated",
    "arg-type",
    "call-arg",
    "import-untyped",
    "attr-defined",
    "return-value",
    "assignment",
    "call-overload",
    "misc",
]

ignore_missing_imports = true
explicit_package_bases = true

# TODO: tighten MyPy checks by enabling these checks over time.
check_untyped_defs = false
disallow_incomplete_defs = false
disallow_untyped_defs = false
disallow_untyped_calls = false
warn_return_any = false

follow_imports = "silent"

[tool.cibuildwheel]
build = "cp3*-manylinux_x86_64"
skip = "pp*"

# see https://developer.nvidia.com/cuda-gpus for compute capabilities
# "CUDA-Enabled Datacenter Products"
# 7.0: V100
# 7.5: T4
# 8.0: A100, A30
# 8.6: A40, A10, A16, A2
# 8.9: L4, L40, L40S
# 9.0: H100
environment = {TORCH_CUDA_ARCH_LIST = "7.0;7.5;8.0;8.6;8.9;9.0"}

# Use the PyTorch manylinux image version '2_28' that contains CUDA 12.8
# and torch 2.7 supports (https://pypi.org/project/torch/2.7.0/#files)
manylinux-x86_64-image = "docker.io/pytorch/manylinux2_28-builder:cuda12.8"

[tool.cibuildwheel.linux]
repair-wheel-command = """
auditwheel repair \
  --plat manylinux_2_28_x86_64 \
  --exclude libtorch.so \
  --exclude libtorch_cuda.so \
  --exclude libtorch_python.so \
  --exclude libtorch_cpu.so \
  --exclude libc10.so \
  --exclude libc10_cuda.so \
  --exclude libcudart.so.12 \
  -w {dest_dir} {wheel}
"""

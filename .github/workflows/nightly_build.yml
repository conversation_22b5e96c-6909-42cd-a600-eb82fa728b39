name: Build nightly container image of latest code

on:
  schedule:
    - cron: '30 7 * * *'

permissions:
  contents: read

jobs:
  nightly-build:
    name: Build image
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
            egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

      - name: Remove toolcache to free space
        run: |
          sudo rm -rf /opt/hostedtoolcache || true
          df -h

      - name: Remove other cruft to free space
        run: |
          docker system prune -af || true
          sudo rm -rf ~/.cache || true
          df -h

      - name: Login to DockerHub
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0

      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # for setuptools-scm
          fetch-depth: 0

      - name: Setup Python 3.11
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: "3.11"

      - name: Get the current date
        run: |
          echo "NOW=$(date +'%Y-%m-%d')" >> "$GITHUB_ENV"

      - name: Build lmcache/vllm-openai container image
        run: |
          docker build \
          --build-arg CUDA_VERSION=12.8 --build-arg UBUNTU_VERSION=24.04 \
          --target image-build \
          --tag lmcache/vllm-openai:latest-nightly --tag lmcache/vllm-openai:nightly-${{ env.NOW }} \
          --file docker/Dockerfile .

      - name: Push lmcache/vllm-openai container image to DockerHub
        run: |
          docker push lmcache/vllm-openai:latest-nightly
          docker push lmcache/vllm-openai:nightly-${{ env.NOW }}

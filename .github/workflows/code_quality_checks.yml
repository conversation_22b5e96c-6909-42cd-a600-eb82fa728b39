name: Code Quality

on:
  pull_request:
  push:
    branches: [dev]

permissions:
  contents: read

jobs:
  pre-commit:
    name: Check code quality
    runs-on: ubuntu-latest
    steps:
        - name: Harden Runner
          uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
          with:
            egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

        - name: Checkout code
          uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
          with:
            # for setuptools-scm
            fetch-depth: 0

        - name: Setup Python 3.12
          uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
          with:
            python-version: "3.12"

        - run: echo "::add-matcher::.github/workflows/matchers/mypy.json"

        - name: Run code quality checks - lint, format, spell, & static checks
          uses: pre-commit/action@2c7b3805fd2a0fd8c1884dcaebf91fc102a13ecd # v3.0.1
          with:
            extra_args: --all-files



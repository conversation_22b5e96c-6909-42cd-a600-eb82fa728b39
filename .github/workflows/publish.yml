name: Build and Publish to PyPI

on:
    # Trigger the workflow on push or pull request,
    # for the dev and any release branches
    push:
        branches:
            - dev
            - "release-**"
        tags:
            - "v*"
    pull_request:
        branches:
            - dev
            - "release-**"
    # Trigger the workflow for when a release is created
    release:
        types:
            - published

env:
    LC_ALL: en_US.UTF-8

defaults:
    run:
        shell: bash

permissions:
    contents: read

jobs:
    # Create release artifacts
    # - build source dist (tar ball) and wheel
    # - upload artifacts to GHA
    build-artifacts:
        name: Build artifacts
        runs-on: ubuntu-latest
        steps:
            - name: "Harden Runner"
              uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
              with:
                egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

            - name: Remove toolcache to free space
              run: |
                sudo rm -rf /opt/hostedtoolcache || true
                df -h

            - name: Remove other cruft to free space
              run: |
                docker system prune -af || true
                sudo rm -rf ~/.cache || true
                df -h

            - name: Checkout code
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                # for setuptools-scm
                fetch-depth: 0

            - name: Setup Python 3.11
              uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
              with:
                python-version: "3.11"

            - name: Install dependencies
              run: |
                python -m pip install --upgrade pip
                pip install build cibuildwheel

            - name: Clean up release artifacts
              run: |
                rm -rf dist/

            - name: Build source distribution (no CUDA)
              run: |
                NO_CUDA_EXT=1 python -m build --sdist

            - name: Build CUDA wheels with cibuildwheel
              env:
                # this builds 3.10, 3.11, 3.12, 3.13 right now
                CIBW_BUILD: "cp3*-manylinux_x86_64"
                CIBW_SKIP: "pp*"
                # see https://developer.nvidia.com/cuda-gpus for compute capabilities
                # "CUDA-Enabled Datacenter Products"
                # 7.0: V100
                # 7.5: T4
                # 8.0: A100, A30
                # 8.6: A40, A10, A16, A2
                # 8.9: L4, L40, L40S
                # 9.0: H100
                CIBW_ENVIRONMENT: "TORCH_CUDA_ARCH_LIST='7.0;7.5;8.0;8.6;8.9;9.0'"
                CIBW_MANYLINUX_X86_64_IMAGE: "docker.io/apostab/manylinux-cuda124"
                CIBW_REPAIR_WHEEL_COMMAND_LINUX: >
                    auditwheel repair
                    --plat manylinux_2_17_x86_64
                    --exclude libtorch.so
                    --exclude libtorch_cuda.so
                    --exclude libtorch_python.so
                    --exclude libtorch_cpu.so
                    --exclude libc10.so
                    --exclude libc10_cuda.so
                    -w {dest_dir} {wheel}
              # continue even if cibuildwheel fails since the wheels are probably
              # built even if some error messages show up
              run: |
                python -m cibuildwheel --output-dir dist || true

            - name: Upload release artifacts to GHA
              uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
              with:
                name: release-artifacts
                path: dist/

    # Push to Test PyPI when:
    # - a new GitHub release is published
    # - a PR is merged into dev branch (push only trigger)
    publish-test-pypi:
        name: Publish packages to test.pypi.org
        if: |
            github.repository_owner == 'LMCache' && (
                github.event.action == 'published' ||
                (github.event_name == 'push' && github.ref == 'refs/heads/dev')
            )
        permissions:
            contents: read
            # see https://docs.pypi.org/trusted-publishers/
            id-token: write
        runs-on: ubuntu-latest
        needs: build-artifacts

        steps:
            - name: Harden Runner
              uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
              with:
                  egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

            - name: Fetch release artifacts
              uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
              with:
                  name: release-artifacts
                  path: dist

            - name: Upload to Test PyPI
              uses: pypa/gh-action-pypi-publish@76f52bc884231f62b9a034ebfe128415bbaabdfc # v1.12.4
              with:
                  repository-url: https://test.pypi.org/legacy/
                  verbose: true

    # Push to PyPI (production) when:
    # - a new GitHub release is created
    publish-pypi:
        name: Publish release to pypi.org
        if: |
            github.repository_owner == 'LMCache' && github.event.action == 'published'
        permissions:
            # see https://docs.pypi.org/trusted-publishers/
            id-token: write
            # allow gh release upload
            contents: write

        runs-on: ubuntu-latest
        needs: build-artifacts

        steps:
            - name: Harden Runner
              uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
              with:
                  egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

            - name: Fetch release artifacts
              uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
              with:
                  name: release-artifacts
                  path: dist

            - name: Upload release artifacts to GitHub release
              env:
                  GITHUB_TOKEN: ${{ github.token }}
              run: >-
                  gh release upload '${{ github.ref_name }}' dist/* --repo '${{ github.repository }}'

            - name: Upload to PyPI
              uses: pypa/gh-action-pypi-publish@76f52bc884231f62b9a034ebfe128415bbaabdfc # v1.12.4
              with:
                verbose: true

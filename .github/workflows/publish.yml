name: Build and Publish to PyPI

on:
    # Trigger the workflow on push or pull request,
    # for the dev and any release branches
    push:
        branches:
            - dev
            - "release-**"
        tags:
            - "v*"
    pull_request:
        branches:
            - dev
            - "release-**"
    # Trigger the workflow for when a release is created
    release:
        types:
            - published

env:
    LC_ALL: en_US.UTF-8

defaults:
    run:
        shell: bash

permissions:
    contents: read

jobs:
    # Create release artifacts
    # - build source dist (tar ball) and wheel
    # - upload artifacts to GHA
    build-artifacts:
        name: Build artifacts
        runs-on: ubuntu-latest
        steps:
            - name: "Harden Runner"
              uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
              with:
                egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

            - name: Print disk space before cleanup
              run: |
                df -h
              shell: bash

            - name: Free Disk Space Linux
              if: runner.os == 'Linux'
              run: |
                sudo docker rmi "$(docker image ls -aq)" >/dev/null 2>&1 || true
                sudo rm -rf \
                  /usr/share/dotnet /usr/local/lib/android /opt/ghc \
                  /usr/local/share/powershell /usr/share/swift /usr/local/.ghcup \
                  /usr/lib/jvm || true
                sudo apt install aptitude -y >/dev/null 2>&1
                sudo aptitude purge '~n ^mysql' -f -y >/dev/null 2>&1
                sudo aptitude purge '~n ^dotnet' -f -y >/dev/null 2>&1
                sudo apt-get autoremove -y >/dev/null 2>&1
                sudo apt-get autoclean -y >/dev/null 2>&1
              shell: bash

            - name: Print disk space after cleanup
              run: |
                df -h
              shell: bash

            - name: Checkout code
              uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
              with:
                # for setuptools-scm
                fetch-depth: 0

            - name: Setup Python 3.11
              uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
              with:
                python-version: "3.11"

            - name: Install dependencies
              run: |
                python -m pip install --upgrade pip
                pip install build cibuildwheel

            - name: Clean up release artifacts
              run: |
                rm -rf dist/

            - name: Build source distribution (no CUDA)
              run: |
                NO_CUDA_EXT=1 python -m build --sdist

            - name: Build CUDA wheels with cibuildwheel
              # Configuration is set in pyproject.toml
              run: |
                python -m cibuildwheel --output-dir dist

            - name: Upload release artifacts to GHA
              uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
              with:
                name: release-artifacts
                path: dist/

    # Push to Test PyPI when:
    # - a new GitHub release is published
    # - a PR is merged into dev branch (push only trigger)
    publish-test-pypi:
        name: Publish packages to test.pypi.org
        if: |
            github.repository_owner == 'LMCache' && (
                github.event.action == 'published' ||
                (github.event_name == 'push' && github.ref == 'refs/heads/dev')
            )
        permissions:
            contents: read
            # see https://docs.pypi.org/trusted-publishers/
            id-token: write
        runs-on: ubuntu-latest
        needs: build-artifacts

        steps:
            - name: Harden Runner
              uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
              with:
                  egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

            - name: Fetch release artifacts
              uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
              with:
                  name: release-artifacts
                  path: dist

            - name: Upload to Test PyPI
              uses: pypa/gh-action-pypi-publish@76f52bc884231f62b9a034ebfe128415bbaabdfc # v1.12.4
              with:
                  repository-url: https://test.pypi.org/legacy/
                  verbose: true

    # Push to PyPI (production) when:
    # - a new GitHub release is created
    publish-pypi:
        name: Publish release to pypi.org
        if: |
            github.repository_owner == 'LMCache' && github.event.action == 'published'
        permissions:
            # see https://docs.pypi.org/trusted-publishers/
            id-token: write
            # allow gh release upload
            contents: write

        runs-on: ubuntu-latest
        needs: build-artifacts

        steps:
            - name: Harden Runner
              uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
              with:
                  egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

            - name: Fetch release artifacts
              uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
              with:
                  name: release-artifacts
                  path: dist

            - name: Upload release artifacts to GitHub release
              env:
                  GITHUB_TOKEN: ${{ github.token }}
              run: >-
                  gh release upload '${{ github.ref_name }}' dist/* --repo '${{ github.repository }}'

            - name: Upload to PyPI
              uses: pypa/gh-action-pypi-publish@76f52bc884231f62b9a034ebfe128415bbaabdfc # v1.12.4
              with:
                verbose: true
